import os
import socket
from email.headerregistry import Address
from pathlib import Path

from corsheaders.defaults import default_headers

from .vault import vault_keys

BASE_DIR = Path(__file__).resolve().parent.parent
SECRET_KEY = vault_keys["SECRET_KEY"]
DEBUG = int(os.environ.get("DEBUG", 1))
APP_DESCRIPTION = os.environ.get("APP_DESCRIPTION", "SageCloud Core API")
ENCRYPTION_KEY = os.environ.get(
    "ENCRYPTION_KEY", "ezgL5gsXkHJyQt9u5GTYgvoE8m6HSTfWazKp-QZ3VxM="
)

ALLOWED_HOSTS = [
    "127.0.0.1",
    "0.0.0.0",
    "localhost",
    "api",
    "host.docker.internal",
]
INTERNAL_IPS = ["127.0.0.1"]
if DEBUG:
    hostname, _, ips = socket.gethostbyname_ex(socket.gethostname())
    INTERNAL_IPS = [ip[:-1] + "1" for ip in ips] + ["127.0.0.1", "********"]

USE_X_FORWARDED_HOST = True
SECURE_PROXY_SSL_HEADER = ("HTTP_X_FORWARDED_PROTO", "https")

# Application definition

INSTALLED_APPS = [
    # "jazzmin",
    "channels",
    "django.contrib.admin",
    "django.contrib.auth",
    "django.contrib.contenttypes",
    "django.contrib.sessions",
    "django.contrib.messages",
    "django.contrib.staticfiles",
    "corsheaders",
    "storages",
    "rest_framework",
    "rest_framework_api_key",
    "django_filters",
    "import_export",
    "debug_toolbar",
    "drf_spectacular",
    "drf_standardized_errors",
    "django_extensions",
    "django_celery_beat",
    "core.celery.CeleryConfig",
    # "pykolofinance",
    "user.apps.UserConfig",
    "business.apps.BusinessConfig",
    "wallet.apps.WalletConfig",
    "transaction.apps.TransactionConfig",
    "ledger.apps.LedgerConfig",
    "virtual_account.apps.VirtualAccountConfig",
    "audit.apps.AuditConfig",
]

MIDDLEWARE = [
    "debug_toolbar.middleware.DebugToolbarMiddleware",
    "corsheaders.middleware.CorsMiddleware",
    "django.middleware.security.SecurityMiddleware",
    "whitenoise.middleware.WhiteNoiseMiddleware",
    "django.contrib.sessions.middleware.SessionMiddleware",
    "django.middleware.common.CommonMiddleware",
    "django.middleware.csrf.CsrfViewMiddleware",
    "django.contrib.auth.middleware.AuthenticationMiddleware",
    "django.contrib.messages.middleware.MessageMiddleware",
    "django.middleware.clickjacking.XFrameOptionsMiddleware",
    # "core.middleware.ValidationErrorMiddleware",
    # 'core.middleware.CaptureExceptionMiddleware',
    # 'core.middleware.RequestResponseLoggerMiddleware',
    # 'pykolofinance.audtilog.logger.APILoggerMiddleware',
    "pykolofinance.audtilog.console.RequestResponseLoggerMiddleware",
]

AUTH_USER_MODEL = "user.User"
ROOT_URLCONF = "core.urls"
IMPORT_EXPORT_USE_TRANSACTIONS = True

SAFE_LIST_IPS = os.getenv("SAFE_LIST_IPS", "").split(",")

TEMPLATES = [
    {
        "BACKEND": "django.template.backends.django.DjangoTemplates",
        "DIRS": [],
        "APP_DIRS": True,
        "OPTIONS": {
            "context_processors": [
                "django.template.context_processors.debug",
                "django.template.context_processors.request",
                "django.contrib.auth.context_processors.auth",
                "django.contrib.messages.context_processors.messages",
            ],
        },
    },
]

WSGI_APPLICATION = "core.wsgi.application"
ASGI_APPLICATION = "core.asgi.application"
CORS_ALLOW_ALL_ORIGINS = True
CSRF_TRUSTED_ORIGINS = [
    "http://0.0.0.0:47001",
    "http://localhost:47001",
    "https://api.sagecloud-core.dev.ercaspay.com",
]
CORS_ALLOW_HEADERS = list(default_headers) + ["X-KMS-TOKEN", "X-Api-Key"]
LOGIN_URL = "rest_framework:login"
LOGOUT_URL = "rest_framework:logout"

JAZZMIN_SETTINGS = {
    "site_title": APP_DESCRIPTION,
    "site_header": APP_DESCRIPTION,
}

# Password validation
# https://docs.djangoproject.com/en/4.0/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        "NAME": "django.contrib.auth.password_validation.UserAttributeSimilarityValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.MinimumLengthValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.CommonPasswordValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.NumericPasswordValidator",
    },
]

# Internationalization
# https://docs.djangoproject.com/en/4.0/topics/i18n/

LANGUAGE_CODE = "en-us"
TIME_ZONE = "Africa/Lagos"
USE_I18N = True
USE_TZ = True

APPEND_SLASH = False

# Email Settings
EMAIL_FROM = Address(
    display_name="Kolomoni MFB", addr_spec=os.environ.get("SENDER_EMAIL")
)
EMAIL_HOST = os.environ.get("SMTP_HOST")
EMAIL_HOST_USER = os.environ.get("SMTP_USER")
EMAIL_HOST_PASSWORD = os.environ.get("SMTP_PASSWORD")
EMAIL_PORT = os.environ.get("SMTP_PORT", 587)
EMAIL_USE_TLS = True

# Others
TOKEN_LIFESPAN = 10  # minutes
CLIENT_URL = os.environ.get("CLIENT_URL")

ENVIRONMENT_INSTANCE = os.environ.get("ENVIRONMENT_INSTANCE", "dev")
VANSO_SYSID = vault_keys["VANSO_SYSID"]
VANSO_PASSWORD = vault_keys["VANSO_PASSWORD"]
VANSO_SENDER = vault_keys["VANSO_SENDER"]
API_KEY_CUSTOM_HEADER = "HTTP_X_API_KEY"

# Google Sign-In
GOOGLE_CLIENT_ID = os.environ.get("GOOGLE_CLIENT_ID")
GOOGLE_CLIENT_SECRET = os.environ.get("GOOGLE_CLIENT_SECRET")

# Connection to VAS Gate
SAGECLOUD_VAS_GATE_BASE_URL = os.environ.get("SAGECLOUD_VAS_GATE_BASE_URL")
SAGECLOUD_VAS_GATE_API_KEY = os.environ.get("SAGECLOUD_VAS_GATE_API_KEY")
EASYPAY_CLIENT_CODE = os.environ.get("EASYPAY_CLIENT_CODE")


KOLOMONI_VA_ACCOUNT_PREFIX = os.environ.get("KOLOMONI_VA_ACCOUNT_PREFIX")
WEMA_VA_ACCOUNT_PREFIX = os.environ.get("WEMA_VA_ACCOUNT_PREFIX")

BASE_ENCRYPTION_KEY = os.environ.get("BASE_ENCRYPTION_KEY")
