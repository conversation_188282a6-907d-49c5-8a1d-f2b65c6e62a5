from enum import Enum

ROLE_OPTIONS = (
    ("Admin", "Admin"),
    ("Initiator", "Initiator"),
    ("Verifier", "Verifier"),
    ("Approver", "Approver"),
    ("Business_Owner", "Business Owner"),
)

GENDER_OPTION = (
    ("Male", "Male"),
    ("Female", "Female"),
)

TOKEN_TYPE = (
    ("CreateToken", "CreateToken"),
    ("ResetToken", "ResetToken"),
)


class PinEnum(Enum):
    Transaction = "Transaction"
    Transfer = "Transfer"


class Auth2FATypeEnums:
    TOTP = "TOTP"
    EMAIL = "EMAIL"
    CHOICES = [
        (TOTP, TOTP),
        (EMAIL, EMAIL),
    ]
