# Generated by Django 5.1.7 on 2025-05-16 16:01

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("user", "0002_user_otp_user_otp_created_at_alter_user_role"),
    ]

    operations = [
        migrations.AddField(
            model_name="user",
            name="two_factor_auth_enabled",
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name="user",
            name="two_factor_auth_secret",
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="user",
            name="two_factor_auth_type",
            field=models.CharField(
                choices=[("TOTP", "TOTP"), ("EMAIL", "EMAIL")],
                default="TOTP",
                max_length=100,
            ),
        ),
    ]
