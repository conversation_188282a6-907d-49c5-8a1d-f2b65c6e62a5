# Generated by Django 5.1.7 on 2025-06-07 14:14

import common.kgs
import django.db.models.deletion
import django.utils.timezone
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="AuditLog",
            fields=[
                (
                    "id",
                    models.CharField(
                        db_index=True,
                        default=common.kgs.generate_unique_id,
                        editable=False,
                        max_length=50,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        db_index=True, default=django.utils.timezone.now
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "email",
                    models.EmailField(
                        db_index=True,
                        help_text="Email of the user who performed the action",
                        max_length=254,
                    ),
                ),
                (
                    "action",
                    models.CharField(
                        choices=[
                            ("LOGIN", "Login"),
                            ("LOGOUT", "Logout"),
                            ("REGISTER", "Registration"),
                            ("PASSWORD_CHANGE", "Password Change"),
                            ("PASSWORD_RESET", "Password Reset"),
                            ("PROFILE_UPDATE", "Profile Update"),
                            ("TRANSACTION_CREATE", "Transaction Created"),
                            ("TRANSACTION_UPDATE", "Transaction Updated"),
                            ("WALLET_CREATE", "Wallet Created"),
                            ("WALLET_UPDATE", "Wallet Updated"),
                            ("BUSINESS_CREATE", "Business Created"),
                            ("BUSINESS_UPDATE", "Business Updated"),
                            ("PIN_CHANGE", "PIN Change"),
                            ("PIN_VERIFY", "PIN Verification"),
                            ("OTP_REQUEST", "OTP Request"),
                            ("OTP_VERIFY", "OTP Verification"),
                            ("TWO_FA_SETUP", "2FA Setup"),
                            ("TWO_FA_DISABLE", "2FA Disable"),
                            ("GOOGLE_AUTH", "Google Authentication"),
                            ("API_ACCESS", "API Access"),
                            ("PERMISSION_CHANGE", "Permission Change"),
                            ("ACCOUNT_DISABLE", "Account Disable"),
                            ("ACCOUNT_ENABLE", "Account Enable"),
                            ("DATA_EXPORT", "Data Export"),
                            ("DATA_IMPORT", "Data Import"),
                            ("SYSTEM_CONFIG", "System Configuration"),
                            ("OTHER", "Other"),
                        ],
                        db_index=True,
                        help_text="Type of action performed",
                        max_length=50,
                    ),
                ),
                (
                    "description",
                    models.TextField(help_text="Detailed description of the action"),
                ),
                (
                    "ip_address",
                    models.GenericIPAddressField(
                        db_index=True,
                        help_text="IP address from which the action was performed",
                    ),
                ),
                (
                    "user_agent",
                    models.TextField(
                        blank=True,
                        help_text="User agent string from the request",
                        null=True,
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("SUCCESS", "Success"),
                            ("FAILED", "Failed"),
                            ("PENDING", "Pending"),
                        ],
                        db_index=True,
                        default="SUCCESS",
                        help_text="Status of the action",
                        max_length=20,
                    ),
                ),
                (
                    "resource_type",
                    models.CharField(
                        blank=True,
                        db_index=True,
                        help_text="Type of resource affected (e.g., User, Transaction, Wallet)",
                        max_length=100,
                        null=True,
                    ),
                ),
                (
                    "resource_id",
                    models.CharField(
                        blank=True,
                        db_index=True,
                        help_text="ID of the resource affected",
                        max_length=100,
                        null=True,
                    ),
                ),
                (
                    "old_values",
                    models.JSONField(
                        blank=True,
                        help_text="Previous values before the change (for updates)",
                        null=True,
                    ),
                ),
                (
                    "new_values",
                    models.JSONField(
                        blank=True,
                        help_text="New values after the change (for updates)",
                        null=True,
                    ),
                ),
                (
                    "metadata",
                    models.JSONField(
                        blank=True,
                        help_text="Additional metadata about the action",
                        null=True,
                    ),
                ),
                (
                    "session_id",
                    models.CharField(
                        blank=True,
                        db_index=True,
                        help_text="Session ID if available",
                        max_length=100,
                        null=True,
                    ),
                ),
                (
                    "request_id",
                    models.CharField(
                        blank=True,
                        db_index=True,
                        help_text="Unique request ID for tracing",
                        max_length=100,
                        null=True,
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        blank=True,
                        help_text="User who performed the action",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="audit_logs",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Audit Log",
                "verbose_name_plural": "Audit Logs",
                "ordering": ["-created_at"],
                "indexes": [
                    models.Index(
                        fields=["user", "-created_at"],
                        name="audit_audit_user_id_429f6b_idx",
                    ),
                    models.Index(
                        fields=["email", "-created_at"],
                        name="audit_audit_email_e25057_idx",
                    ),
                    models.Index(
                        fields=["action", "-created_at"],
                        name="audit_audit_action_0c6a84_idx",
                    ),
                    models.Index(
                        fields=["ip_address", "-created_at"],
                        name="audit_audit_ip_addr_ff6f1d_idx",
                    ),
                    models.Index(
                        fields=["status", "-created_at"],
                        name="audit_audit_status_605d18_idx",
                    ),
                    models.Index(
                        fields=["resource_type", "resource_id"],
                        name="audit_audit_resourc_2a3aef_idx",
                    ),
                ],
            },
        ),
    ]
