import uuid

from django.utils import timezone

from .models import AuditLog


def get_client_ip(request):
    """
    Get the client IP address from the request
    """
    x_forwarded_for = request.META.get("HTTP_X_FORWARDED_FOR")
    if x_forwarded_for:
        ip = x_forwarded_for.split(",")[0]
    else:
        ip = request.META.get("REMOTE_ADDR")
    return ip


def get_user_agent(request):
    """
    Get the user agent from the request
    """
    return request.META.get("HTTP_USER_AGENT", "")


def generate_request_id():
    """
    Generate a unique request ID for tracing
    """
    return str(uuid.uuid4())


def log_user_action(
    request,
    user=None,
    action=None,
    description=None,
    status=AuditLog.SUCCESS,
    resource_type=None,
    resource_id=None,
    old_values=None,
    new_values=None,
    metadata=None,
):
    """
    Utility function to log user actions

    Args:
        request: Django request object
        user: User instance (optional, will try to get from request)
        action: Action type (from AuditLog.ACTION_CHOICES)
        description: Description of the action
        status: Status of the action (SUCCESS, FAILED, PENDING)
        resource_type: Type of resource affected
        resource_id: ID of the resource affected
        old_values: Previous values (for updates)
        new_values: New values (for updates)
        metadata: Additional metadata

    Returns:
        AuditLog instance
    """
    # Get user from request if not provided
    if not user and hasattr(request, "user") and request.user.is_authenticated:
        user = request.user

    # Get email
    email = user.email if user else "<EMAIL>"

    # Get IP address and user agent
    ip_address = get_client_ip(request)
    user_agent = get_user_agent(request)

    # Generate request ID
    request_id = generate_request_id()

    # Get session ID if available
    session_id = request.session.session_key if hasattr(request, "session") else None

    return AuditLog.log_action(
        user=user,
        email=email,
        action=action,
        description=description,
        ip_address=ip_address,
        user_agent=user_agent,
        status=status,
        resource_type=resource_type,
        resource_id=resource_id,
        old_values=old_values,
        new_values=new_values,
        metadata=metadata,
        session_id=session_id,
        request_id=request_id,
    )


def log_login_attempt(request, user=None, success=True, reason=None):
    """
    Log login attempts
    """
    status = AuditLog.SUCCESS if success else AuditLog.FAILED
    description = "User logged in successfully"
    if not success:
        description = f"Login failed: {reason}" if reason else "Login failed"

    metadata = {
        "login_method": "email_password",
        "timestamp": timezone.now().isoformat(),
    }

    return log_user_action(
        request=request,
        user=user,
        action=AuditLog.LOGIN,
        description=description,
        status=status,
        resource_type="User",
        resource_id=user.id if user else None,
        metadata=metadata,
    )


def log_logout(request, user=None):
    """
    Log logout actions
    """
    return log_user_action(
        request=request,
        user=user,
        action=AuditLog.LOGOUT,
        description="User logged out",
        resource_type="User",
        resource_id=user.id if user else None,
    )


def log_registration(request, user=None, registration_type="email"):
    """
    Log user registration
    """
    metadata = {
        "registration_type": registration_type,
        "timestamp": timezone.now().isoformat(),
    }

    return log_user_action(
        request=request,
        user=user,
        action=AuditLog.REGISTER,
        # status=AuditLog.SUCCESS if user else AuditLog.FAILED,
        description=f"New user registered via {registration_type}",
        resource_type="User",
        resource_id=user.id if user else None,
        metadata=metadata,
    )


def log_password_change(request, user=None, success=True):
    """
    Log password change attempts
    """
    status = AuditLog.SUCCESS if success else AuditLog.FAILED
    description = (
        "Password changed successfully" if success else "Password change failed"
    )

    return log_user_action(
        request=request,
        user=user,
        action=AuditLog.PASSWORD_CHANGE,
        description=description,
        status=status,
        resource_type="User",
        resource_id=user.id if user else None,
    )


def log_profile_update(request, user=None, old_data=None, new_data=None):
    """
    Log profile updates
    """
    return log_user_action(
        request=request,
        user=user,
        action=AuditLog.PROFILE_UPDATE,
        description="User profile updated",
        resource_type="User",
        resource_id=user.id if user else None,
        old_values=old_data,
        new_values=new_data,
    )


def log_transaction_action(
    request, user=None, action_type="CREATE", transaction=None, description=None
):
    """
    Log transaction-related actions
    """
    action = (
        AuditLog.TRANSACTION_CREATE
        if action_type == "CREATE"
        else AuditLog.TRANSACTION_UPDATE
    )

    if not description:
        description = f"Transaction {action_type.lower()}"

    return log_user_action(
        request=request,
        user=user,
        action=action,
        description=description,
        resource_type="Transaction",
        resource_id=transaction.id if transaction else None,
        metadata={
            "transaction_reference": transaction.reference if transaction else None,
            "amount": str(transaction.amount) if transaction else None,
        },
    )


def log_otp_action(request, user=None, action_type="REQUEST", success=True):
    """
    Log OTP-related actions
    """
    action = AuditLog.OTP_REQUEST if action_type == "REQUEST" else AuditLog.OTP_VERIFY
    status = AuditLog.SUCCESS if success else AuditLog.FAILED

    description = f"OTP {action_type.lower()} "
    description += "successful" if success else "failed"

    return log_user_action(
        request=request,
        user=user,
        action=action,
        description=description,
        status=status,
        resource_type="User",
        resource_id=user.id if user else None,
    )
