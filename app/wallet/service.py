import logging
from decimal import Decimal

from business.models import Business
from django.db import models, transaction
from transaction.enums import (
    TransactionClassEnum,
    TransactionModeEnum,
    TransactionStatusEnum,
)
from transaction.models import Transaction
from uuid6 import uuid7
from wallet.exceptions import WalletException
from wallet.models import Wallet

logger = logging.getLogger(__name__)


class WalletService:
    def __init__(self, wallet: Wallet):
        """
        Initialize a WalletService instance for a given Wallet instance.

        Args:
            wallet (Wallet): Wallet instance to perform operations on.
        """
        self.wallet = wallet

    @transaction.atomic
    def debit(
        self,
        amount: Decimal,
        *,
        business: "Business",
        txn_class: TransactionClassEnum,
        type: str,
        narration: str,
        merchant_reference: str,
        validate_balance: bool = True,
        impact_wallet: bool = True,
    ) -> Transaction:
        """
        Debit a given amount from the wallet.

        Args:
            amount (Decimal): The amount to debit from the wallet.
            business (Business): The business associated with the wallet.
            txn_class (str): The transaction class.
            type (str): The transaction type.
            narration (str): A description of the debit transaction.
            merchant_reference (str): A reference provided by the merchant.
            validate_balance (bool, optional): If True (default), checks if the
                wallet has sufficient balance before debiting. If False, skips
                the balance check.
            impact_wallet (bool, optional): If True (default), debits the wallet
                balance. If False, does not modify the wallet balance.

        Returns:
            Transaction: The transaction instance created.
        """
        if amount <= 0:
            raise WalletException("Debit amount must be positive.")

        self.wallet.refresh_from_db(fields=["balance"])

        charge = self._get_transaction_charge(txn_class, amount)
        net_amount = self._compute_net_amount(txn_class, amount, charge)
        if validate_balance and self.wallet.balance < net_amount:
            raise WalletException(f"Insufficient wallet balance to debit {amount}")

        old_balance = self.wallet.balance

        if impact_wallet:
            self.wallet.balance = models.F("balance") - net_amount
            self.wallet.save(update_fields=["balance"])
            self.wallet.refresh_from_db(fields=["balance"])

        new_balance = self.wallet.balance

        txn = Transaction.objects.create(
            wallet=self.wallet,
            business=business,
            reference=self._generate_reference(),
            merchant_reference=merchant_reference,
            status=TransactionStatusEnum.PENDING.value,  # Mark as PENDING initially
            mode=TransactionModeEnum.DEBIT.value,
            txn_class=txn_class,
            type=type,
            amount=amount,
            charge=charge,
            net_amount=net_amount,
            old_balance=old_balance,
            new_balance=new_balance,
            narration=narration,
            is_wallet_impacted=impact_wallet,
        )

        return txn

    @transaction.atomic
    def credit(
        self,
        amount: Decimal,
        *,
        business: "Business",
        txn_class: TransactionClassEnum,
        type: str,
        narration: str,
        merchant_reference: str,
        impact_wallet: bool = True,
    ) -> Transaction:
        """
        Credits the specified amount to the wallet and records the transaction.

        Args:
            amount (Decimal): The amount to be credited to the wallet.
            business (Business): The business associated with the wallet.
            txn_class (str): The transaction class, indicating the category of the transaction.
            type (str): The type of transaction being processed.
            narration (str): A description or note associated with the transaction.
            merchant_reference (str): A unique reference provided by the merchant for the transaction.
            impact_wallet (bool, optional): If True, the wallet balance is updated. Defaults to True.

        Returns:
            Transaction: The created transaction record.

        Raises:
            WalletException: If the credit amount is not positive.
        """

        if amount <= 0:
            raise WalletException("Credit amount must be positive.")

        self.wallet.refresh_from_db(fields=["balance"])

        old_balance = self.wallet.balance

        if impact_wallet:
            self.wallet.balance = models.F("balance") + amount
            self.wallet.save(update_fields=["balance"])
            self.wallet.refresh_from_db(fields=["balance"])

        new_balance = self.wallet.balance

        txn = Transaction.objects.create(
            wallet=self.wallet,
            business=business,
            reference=self._generate_reference(),
            merchant_reference=merchant_reference,
            status=TransactionStatusEnum.SUCCESSFUL.value,
            mode=TransactionModeEnum.CREDIT.value,
            txn_class=txn_class,
            type=type,
            amount=amount,
            old_balance=old_balance,
            new_balance=new_balance,
            narration=narration,
            is_wallet_impacted=impact_wallet,
        )

        return txn

    def _generate_reference(self) -> str:
        return uuid7().hex.upper()

    def _compute_net_amount(
        self, txn_class: TransactionClassEnum, amount: Decimal, charge: Decimal
    ) -> Decimal:
        """
        Calculate the net amount for a transaction based on the transaction class.

        For 'VIRTUAL_ACCOUNT' transactions, the net_amount is calculated as amount - charge.
        For 'TRANSFER' and other VAS transactions, the net_amount is calculated as amount + charge.

        Args:
            transaction_class (str): The class of the transaction (e.g. 'VAS', 'Virtual Account')
            amount (float): The transaction amount
            charge (float): The transaction charge

        Returns:
            float: The calculated net amount
        """
        match txn_class:
            case TransactionClassEnum.VIRTUAL_ACCOUNT.value:
                net = amount - charge
                return net
            case _:
                net = amount + charge
                return net

    def _get_transaction_charge(
        self, txn_class: TransactionClassEnum, amount: Decimal
    ) -> Decimal:
        """
        Helper function to retrieve the corresponding for a transaction based on the transaction class and amount.

        For 'TRANSFER' & 'VIRTUAL_ACCOUNT' transactions, the charge is computed from fee settings.
        Other VAS transactions have charge default to 0.

        Args:
            transaction_class (str): The class of the transaction (e.g. 'AIRTIME', 'TRANSFER')
            amount (float): The transaction amount

        Returns:
            Decimal: The calculated charge
        """
        TRANSFER_FEE = 100  # TODO: Get from merchant fee settings
        VIRTUAL_ACCOUNT_FEE = 50  # TODO: Get from merchant fee settings
        match txn_class:
            case TransactionClassEnum.TRANSFER.value:
                return Decimal(TRANSFER_FEE)
            case TransactionClassEnum.VIRTUAL_ACCOUNT.value:
                return Decimal(VIRTUAL_ACCOUNT_FEE)
            case _:
                return Decimal(0)
