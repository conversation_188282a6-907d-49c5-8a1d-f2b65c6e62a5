class WalletEnums:
    GE<PERSON>RA<PERSON> = "GENERAL"
    ADMIN = "ADMIN"
    COMMISSION = "COMMISSION"
    WEMA_VIRTUAL_ACCOUNT = "WEMA_VIRTUAL_ACCOUNT"
    KOLOMONI_VIRTUAL_ACCOUNT = "<PERSON><PERSON><PERSON><PERSON><PERSON>_VIRTUAL_ACCOUNT"
    ACCESS_VIRTUAL_ACCOUNT = "ACCESS_VIRTUAL_ACCOUNT"
    RECURRING_DEBIT = "RECURRING_DEBIT"

    WALLET_TYPE_CHOICES = [
        (GENERAL, GENERAL),
        (ADMIN, ADMIN),
        (COMMISSION, COMMISSION),
        (WEMA_VIRTUAL_ACCOUNT, WEMA_VIRTUAL_ACCOUNT),
        (ACCESS_VIRTUAL_ACCOUNT, ACCESS_VIRTUAL_ACCOUNT),
        (KOLOMONI_VIRTUAL_ACCOUNT, K<PERSON><PERSON>ONI_VIRTUAL_ACCOUNT),
        (RECURRING_DEBIT, RECURRING_DEBIT),
    ]

    def va_wallets(self):
        return [
            self.WEMA_VIRTUAL_ACCOUNT,
            self.KOLOMONI_VIRTUAL_ACCOUNT,
            self.ACCESS_VIRTUAL_ACCOUNT,
        ]
