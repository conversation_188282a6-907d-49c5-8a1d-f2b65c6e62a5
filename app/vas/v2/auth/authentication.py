from business.models import Business
from rest_framework.authentication import BaseAuthentication
from vas.v2.auth.exceptions import BusinessAuthenticationException
from vas.v2.auth.utils import decode_token


class BusinessJWTAuthentication(BaseAuthentication):
    def authenticate(self, request):
        auth_header = request.headers.get("Authorization")
        if not auth_header or not auth_header.startswith("Bearer "):
            raise BusinessAuthenticationException("Authentication error", 401)

        token = auth_header.split(" ")[1]
        payload = decode_token(token)
        business = Business.objects.filter(id=payload["business_id"]).first()

        if not business:
            raise BusinessAuthenticationException("Authentication error", 401)

        return business, None
