import logging

from common.responses import ApiResponse
from rest_framework import serializers
from vas.v2.auth.handlers.basic_auth_handler import BasicAuthHandler

logger = logging.getLogger(__name__)


class BusinessAuthorizationSerializer(serializers.Serializer):

    def save(self, **kwargs):
        header = self.context["header"]

        result = BasicAuthHandler().authenticate(header)

        return ApiResponse(result).to_dict()
