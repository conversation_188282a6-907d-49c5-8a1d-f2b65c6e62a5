import logging

from business.models import Business
from common.enums import DataNetworkEnum
from rest_framework import serializers
from transaction.dtos import CreateBaseTransactionParams
from transaction.enums import TransactionClassEnum
from transaction.handlers.base import TransactionHandler
from transaction.models.base import Transaction
from vas.integrations.data import DataVASGateClient

logger = logging.getLogger(__name__)


class DataLookupRequestSerializer(serializers.Serializer):
    network = serializers.ChoiceField(choices=DataNetworkEnum.choices())
    phone = serializers.CharField(allow_blank=True)

    def save(self, **kwargs):
        payload = self.validated_data
        # network = payload.get("network")
        # phone = payload.get("phone")
        client = DataVASGateClient()
        status_code, response = client.data_lookup(payload)
        return response


class DataPurchaseRequestSerializer(serializers.Serializer):

    data_code = serializers.CharField(max_length=50)
    amount = serializers.CharField(max_length=20)
    phone = serializers.CharField(max_length=15)
    network = serializers.ChoiceField(choices=DataNetworkEnum.choices())
    reference = serializers.CharField(max_length=100)

    def validate_reference(self, value):
        if Transaction.objects.filter(merchant_reference=value).exists():
            raise serializers.ValidationError("Reference already exists")
        return value

    def save(self, **kwargs):
        payload = self.validated_data
        data_code = payload.get("data_code")
        amount = payload.get("amount")
        phone = payload.get("phone")
        network = payload.get("network")
        reference = payload.get("reference")

        business: Business = self.context["business"]
        wallet = business.get_general_wallet()
        handler = TransactionHandler()

        params = CreateBaseTransactionParams(
            wallet=wallet,
            business=business,
            amount=amount,
            txn_class=TransactionClassEnum.DATA.value,
            type=network,
            narration=f"{network} Data Purchase",
            reference=reference,
        )
        txn = handler.create_base_transaction(params)
        vas_extra_fields = {
            "network": network,
            "phone_number": phone,
            "data_code": data_code,
        }
        vas_txn, ledger_txn = handler.create_vas_transaction_and_ledger_entry(
            txn=txn,
            txn_class=TransactionClassEnum.DATA.value,
            extra_fields=vas_extra_fields,
        )
        client = DataVASGateClient()
        status_code, response = client.data_purchase(payload)
        response_status = response.get("status")

        handler.update_base_transaction_status(
            txn=txn,
            status=response_status,
        )
        update_fields = {
            "status": response_status,
        }
        handler.update_vas_transaction(txn, vas_txn, update_fields)
        return response
