import logging

from business.models import Business
from common.enums import AirtimeNetworkEnum
from rest_framework import serializers
from transaction.dtos import CreateBaseTransactionParams
from transaction.enums import TransactionClassEnum
from transaction.handlers.base import TransactionHandler
from transaction.models.airtime import AirtimeVASTransaction
from vas.integrations.airtime import AirtimeVASGateClient

logger = logging.getLogger(__name__)


class AirtimePurchaseRequestSerializer(serializers.Serializer):
    network = serializers.ChoiceField(choices=AirtimeNetworkEnum.choices())
    reference = serializers.CharField()
    phone = serializers.CharField()
    amount = serializers.IntegerField()

    def save(self, **kwargs):
        payload = self.validated_data
        network = payload.get("network")
        amount = payload.get("amount")
        reference = payload.get("reference")
        phone_number = payload.get("phone")

        business: Business = self.context["business"]
        wallet = business.get_general_wallet()
        handler = TransactionHandler()

        params = CreateBaseTransactionParams(
            wallet=wallet,
            business=business,
            amount=amount,
            txn_class=TransactionClassEnum.AIRTIME.value,
            type=network,
            narration=f"{network} Airtime Purchase",
            reference=reference,
        )
        txn = handler.create_base_transaction(params)
        vas_extra_fields = {
            "network": network,
            "phone_number": phone_number,
        }
        vas_txn, ledger_txn = handler.create_vas_transaction_and_ledger_entry(
            txn=txn,
            txn_class=TransactionClassEnum.AIRTIME.value,
            extra_fields=vas_extra_fields,
        )
        client = AirtimeVASGateClient()
        status_code, response = client.purchase_airtime(payload)
        response_status = response.get("status")

        handler.update_base_transaction_status(
            txn=txn,
            status=response_status,
        )
        update_fields = {
            "status": response_status,
        }
        handler.update_vas_transaction(txn, vas_txn, update_fields)
        return response


class AirtimeVASTransactionSerializer(serializers.ModelSerializer):

    class Meta:
        model = AirtimeVASTransaction
        fields = "__all__"
