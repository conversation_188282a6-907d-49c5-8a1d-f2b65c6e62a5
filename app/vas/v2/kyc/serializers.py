import logging

from business.models import Business
from common.enums import KYCTypeEnum
from common.kgs import generate_uuid7
from rest_framework import serializers
from transaction.dtos import CreateBaseTransactionParams
from transaction.enums import TransactionClassEnum
from transaction.handlers.base import TransactionHandler
from vas.integrations.kyc import KYCVASGateClient

logger = logging.getLogger(__name__)
BVN_VERIFICATION_FEE = 200  # TODO: Get from merchant fee settings
NIN_VERIFICATION_FEE = 100  # TODO: Get from merchant fee settings
PHONE_LOOKUP_VERIFICATION_FEE = 150  # TODO: Get from merchant fee settings


class VerifyBVNSerializer(serializers.Serializer):
    bvn = serializers.CharField(max_length=11, min_length=11)
    phone = serializers.CharField(required=False)

    def save(self, **kwargs):
        payload = self.validated_data
        business: Business = self.context["business"]
        wallet = business.get_general_wallet()
        handler = TransactionHandler()

        type = KYCTypeEnum.BVN.value
        params = CreateBaseTransactionParams(
            wallet=wallet,
            business=business,
            amount=BVN_VERIFICATION_FEE,
            txn_class=TransactionClassEnum.KYC.value,
            type=type,
            narration=f"KYC Verification - {type}",
            reference=generate_uuid7(),
        )
        txn = handler.create_base_transaction(params)
        vas_extra_fields = {
            "type": type,
        }
        vas_txn, ledger_txn = handler.create_vas_transaction_and_ledger_entry(
            txn=txn,
            txn_class=TransactionClassEnum.KYC.value,
            extra_fields=vas_extra_fields,
        )
        client = KYCVASGateClient()
        status_code, response = client.verify_bvn(payload)
        response_status = response.get("status")

        handler.update_base_transaction_status(
            txn=txn,
            status=response_status,
        )
        update_fields = {
            "status": response_status,
        }
        handler.update_vas_transaction(txn, vas_txn, update_fields)
        return response


class VerifyNINSerializer(serializers.Serializer):
    nin = serializers.CharField()
    phone = serializers.CharField(required=False)

    def save(self, **kwargs):
        payload = self.validated_data
        business: Business = self.context["business"]
        wallet = business.get_general_wallet()
        handler = TransactionHandler()

        type = KYCTypeEnum.NIN.value
        params = CreateBaseTransactionParams(
            wallet=wallet,
            business=business,
            amount=NIN_VERIFICATION_FEE,
            txn_class=TransactionClassEnum.KYC.value,
            type=type,
            narration=f"KYC Verification - {type}",
            reference=generate_uuid7(),
        )
        txn = handler.create_base_transaction(params)
        vas_extra_fields = {
            "type": type,
        }
        vas_txn, ledger_txn = handler.create_vas_transaction_and_ledger_entry(
            txn=txn,
            txn_class=TransactionClassEnum.KYC.value,
            extra_fields=vas_extra_fields,
        )
        client = KYCVASGateClient()
        status_code, response = client.verify_nin(payload)
        response_status = response.get("status")

        handler.update_base_transaction_status(
            txn=txn,
            status=response_status,
        )
        update_fields = {
            "status": response_status,
        }
        handler.update_vas_transaction(txn, vas_txn, update_fields)
        return response


class PhoneNumberLookupSerializer(serializers.Serializer):
    phone = serializers.CharField()

    def save(self, **kwargs):
        payload = self.validated_data
        business: Business = self.context["business"]
        wallet = business.get_general_wallet()
        handler = TransactionHandler()

        type = KYCTypeEnum.PHONE_NUMBER.value
        params = CreateBaseTransactionParams(
            wallet=wallet,
            business=business,
            amount=PHONE_LOOKUP_VERIFICATION_FEE,
            txn_class=TransactionClassEnum.KYC.value,
            type=type,
            narration=f"KYC Verification - {type}",
            reference=generate_uuid7(),
        )
        txn = handler.create_base_transaction(params)
        vas_extra_fields = {
            "type": type,
        }
        vas_txn, ledger_txn = handler.create_vas_transaction_and_ledger_entry(
            txn=txn,
            txn_class=TransactionClassEnum.KYC.value,
            extra_fields=vas_extra_fields,
        )
        client = KYCVASGateClient()
        status_code, response = client.lookup_phone_number(payload)

        response_status = response.get("status")

        handler.update_base_transaction_status(
            txn=txn,
            status=response_status,
        )
        update_fields = {
            "status": response_status,
        }
        handler.update_vas_transaction(txn, vas_txn, update_fields)
        return response
