from common.decorators import merchant_onboarding_required
from drf_spectacular.utils import extend_schema, extend_schema_view
from rest_framework import generics, permissions, status
from rest_framework.response import Response

from .serializers import (
    PhoneNumberLookupSerializer,
    VerifyBVNSerializer,
    VerifyNINSerializer,
)


@extend_schema_view(
    post=extend_schema(
        summary="Verify BVN",
        tags=["vas-kyc"],
    )
)
class VerifyBVNView(generics.GenericAPIView):
    serializer_class = VerifyBVNSerializer
    permission_classes = [permissions.IsAuthenticated]

    @merchant_onboarding_required
    def post(self, request, *args, **kwargs):
        business = request.business
        serializer = self.get_serializer(
            data=request.data, context={"business": business}
        )
        serializer.is_valid(raise_exception=True)
        data = serializer.save()
        return Response(data, status=status.HTTP_200_OK)


@extend_schema_view(
    post=extend_schema(
        summary="Verify NIN",
        tags=["vas-kyc"],
    )
)
class VerifyNINView(generics.GenericAPIView):
    serializer_class = VerifyNINSerializer
    permission_classes = [permissions.IsAuthenticated]

    @merchant_onboarding_required
    def post(self, request, *args, **kwargs):
        business = request.business
        serializer = self.get_serializer(
            data=request.data, context={"business": business}
        )
        serializer.is_valid(raise_exception=True)
        data = serializer.save()
        return Response(data, status=status.HTTP_200_OK)


@extend_schema_view(
    post=extend_schema(
        summary="Phone Number Lookup",
        tags=["vas-kyc"],
    )
)
class PhoneNumberLookupView(generics.GenericAPIView):
    serializer_class = PhoneNumberLookupSerializer
    permission_classes = [permissions.IsAuthenticated]

    @merchant_onboarding_required
    def post(self, request, *args, **kwargs):
        business = request.business
        serializer = self.get_serializer(
            data=request.data, context={"business": business}
        )
        serializer.is_valid(raise_exception=True)
        data = serializer.save()
        return Response(data, status=status.HTTP_200_OK)


@extend_schema_view(
    post=extend_schema(
        summary="Verify BVN with Image",
        tags=["vas-kyc"],
    )
)
class VerifyBVNWithImageView(generics.GenericAPIView):
    serializer_class = VerifyBVNSerializer
    permission_classes = [permissions.IsAuthenticated]

    @merchant_onboarding_required
    def post(self, request, *args, **kwargs):
        business = request.business
        serializer = self.get_serializer(
            data=request.data, context={"business": business}
        )
        serializer.is_valid(raise_exception=True)
        data = serializer.save()
        return Response(data, status=status.HTTP_200_OK)
