import logging

from business.models import Business
from common.enums import ElectricityBiller
from rest_framework import serializers
from transaction.dtos import CreateBaseTransactionParams
from transaction.enums import TransactionClassEnum
from transaction.handlers.base import TransactionHandler
from transaction.models.base import Transaction
from vas.integrations.electricity import ElectricityVASGateClient

logger = logging.getLogger(__name__)


class FetchElectricityBillersSerializer(serializers.Serializer):
    def save(self, **kwargs):
        client = ElectricityVASGateClient()
        status_code, response = client.fetch_billers()
        return response


class ValidateMeterRequestSerializer(serializers.Serializer):
    account_number = serializers.CharField()
    type = serializers.ChoiceField(choices=ElectricityBiller.choices())

    def save(self, **kwargs):
        payload = self.validated_data
        meter_number = payload.get("account_number")
        biller = payload.get("type")
        client = ElectricityVASGateClient()
        payload = {"meter_number": meter_number, "biller": biller}
        status_code, response = client.validate_meter(payload)
        return response


class ElectricityPurchaseRequestSerializer(serializers.Serializer):
    account_number = serializers.CharField()
    amount = serializers.IntegerField()
    reference = serializers.CharField()
    phone = serializers.CharField()
    type = serializers.ChoiceField(choices=ElectricityBiller.choices())

    def validate_reference(self, value):
        if Transaction.objects.filter(merchant_reference=value).exists():
            raise serializers.ValidationError("Reference already exists")
        return value

    def save(self, **kwargs):
        payload = self.validated_data
        meter_number = payload.get("account_number")
        amount = payload.get("amount")
        reference = payload.get("reference")
        phone = payload.get("phone")

        biller_enum = payload.get("type")
        biller = ElectricityBiller(biller_enum)
        type = biller.title()

        business: Business = self.context["business"]
        wallet = business.get_general_wallet()
        handler = TransactionHandler()

        params = CreateBaseTransactionParams(
            wallet=wallet,
            business=business,
            amount=amount,
            txn_class=TransactionClassEnum.ELECTRICITY.value,
            type=type,
            narration=f"Electricity Purchase - {type}",
            reference=reference,
        )
        txn = handler.create_base_transaction(params)
        vas_extra_fields = {
            "meter_number": meter_number,
            "biller": biller,
            "phone_number": phone,
            "disco": biller.disco,
            "disco_type": biller.disco_type,
        }
        vas_txn, ledger_txn = handler.create_vas_transaction_and_ledger_entry(
            txn=txn,
            txn_class=TransactionClassEnum.ELECTRICITY.value,
            extra_fields=vas_extra_fields,
        )
        client = ElectricityVASGateClient()
        payload["email"] = business.email if business.email else business.owner.email
        payload["biller"] = biller
        payload["meter_number"] = meter_number
        status_code, response = client.purchase_electricity(payload)
        response_status = response.get("status")

        handler.update_base_transaction_status(
            txn=txn,
            status=response_status,
        )
        update_fields = {
            "status": response_status,
        }
        handler.update_vas_transaction(txn, vas_txn, update_fields)
        return response
