import logging

from vas.integrations.base import BaseVASGateClient

logger = logging.getLogger(__name__)


class EducationVASGateClient(BaseVASGateClient):
    def __init__(self):
        super().__init__()

    def waec_lookup(self, payload) -> tuple[int, dict]:
        response, status_code = self._make_request(
            method="POST",
            url=f"{self.base_url}/api/v1/education/shago-waec/lookup/",
            data=payload,
        )
        return self._map_response(response, status_code)

    def waec_purchase(self, payload) -> tuple[int, dict]:
        response, status_code = self._make_request(
            method="POST",
            url=f"{self.base_url}/api/v1/education/shago-waec/purchase/",
            data=payload,
        )
        return self._map_response(response, status_code)

    def jamb_lookup(self, payload) -> tuple[int, dict]:
        response, status_code = self._make_request(
            method="POST",
            url=f"{self.base_url}/api/v1/education/shago-jamb/options/",
            data=payload,
        )
        return self._map_response(response, status_code)

    def jamb_validate(self, payload) -> tuple[int, dict]:
        response, status_code = self._make_request(
            method="POST",
            url=f"{self.base_url}/api/v1/education/shago-jamb/validate/",
            data=payload,
        )
        return self._map_response(response, status_code)

    def jamb_purchase(self, payload) -> tuple[int, dict]:
        response, status_code = self._make_request(
            method="POST",
            url=f"{self.base_url}/api/v1/education/shago-jamb/purchase/",
            data=payload,
        )
        return self._map_response(response, status_code)
