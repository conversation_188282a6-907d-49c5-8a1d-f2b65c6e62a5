from common.pagination import LargeDatasetKeySetPagination
from common.serializers import EmptySerializer
from django.db.models import Count, Q, Sum
from rest_framework import status, viewsets
from rest_framework.decorators import action
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from transaction.enums import TransactionModeEnum, TransactionStatusEnum
from transaction.models.virtual_account import VirtualAccountVasTransaction
from virtual_account.models import VirtualAccount
from virtual_account.v1.serializers import (
    VirtualAccountSerializer,
    VirtualAccountTransactionSerializer,
)
from wallet.enums import WalletEnums
from wallet.models import Wallet


class VirtualAccountViewSet(viewsets.ModelViewSet):
    queryset = VirtualAccount.objects.all()
    serializer_class = VirtualAccountSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        queryset = super().get_queryset()
        if self.request.user.role == "Business_Owner":
            queryset = queryset.filter(business=self.request.user.business)
        return queryset

    def create(self, request, *args, **kwargs):
        return Response(status=status.HTTP_405_METHOD_NOT_ALLOWED)

    def update(self, request, *args, **kwargs):
        return Response(status=status.HTTP_405_METHOD_NOT_ALLOWED)

    def destroy(self, request, *args, **kwargs):
        return Response(status=status.HTTP_405_METHOD_NOT_ALLOWED)

    @action(
        methods=["GET"],
        detail=False,
        url_path="total-accounts",
        serializer_class=EmptySerializer,
    )
    def total_accounts(self, request):
        return Response(
            {
                "total_accounts": self.get_queryset().count(),
            },
            status=status.HTTP_200_OK,
        )


class VirtualAccountTransactionsViewSet(viewsets.ModelViewSet):
    serializer_class = VirtualAccountTransactionSerializer
    permission_classes = [IsAuthenticated]
    pagination_class = LargeDatasetKeySetPagination
    ordering_fields = ["created_at"]

    def get_queryset(self):
        queryset = VirtualAccountVasTransaction.objects.select_related(
            "business"
        ).values(
            "session_id",
            # Source details
            "source_account_number",
            "source_account_name",
            "source_bank_name",
            "source_bank_code",
            # Recipient details
            "recipient_account_number",
            "recipient_account_name",
            "recipient_bank_name",
            "recipient_bank_code",
            # Transaction details
            "reference",
            "merchant_reference",
            "status",
            "mode",
            "amount",
            "charge",
            "net_amount",
            "narration",
            # Business info from related model
            "business__id",
            "business__name",
            "created_at",
        )
        if self.request.user.role == "Business_Owner":
            queryset = queryset.filter(business=self.request.user.business)
        return queryset

    def create(self, request, *args, **kwargs):
        return Response(status=status.HTTP_405_METHOD_NOT_ALLOWED)

    def update(self, request, *args, **kwargs):
        return Response(status=status.HTTP_405_METHOD_NOT_ALLOWED)

    def destroy(self, request, *args, **kwargs):
        return Response(status=status.HTTP_405_METHOD_NOT_ALLOWED)

    @action(
        methods=["GET"],
        detail=False,
        url_path="overview",
        serializer_class=EmptySerializer,
    )
    def overview(self, request, *args, **kwargs):
        totals = (
            self.get_queryset()
            .filter(status=TransactionStatusEnum.SUCCESSFUL.value)
            .aggregate(
                inflows=Count("id", filter=Q(mode=TransactionModeEnum.CREDIT.value)),
                outflows=Count("id", filter=Q(mode=TransactionModeEnum.DEBIT.value)),
            )
        )

        return Response(
            {
                "total_inflows": totals.get("inflows", 0),
                "total_outflows": totals.get("outflows", 0),
            }
        )

    @action(
        methods=["GET"],
        detail=False,
        url_path="total-balance",
        serializer_class=EmptySerializer,
    )
    def total_balance(self, request):
        wallet_qs = Wallet.objects.filter(
            business=self.request.user.business, type__in=WalletEnums().va_wallets()
        )

        total_balance = wallet_qs.aggregate(total=Sum("balance"))["total"] or 0
        return Response(
            {
                "total_balance": total_balance,
            },
            status=status.HTTP_200_OK,
        )
