# Generated by Django 5.1.7 on 2025-06-04 21:58

import common.kgs
import django.db.models.deletion
import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("business", "0006_alter_business_name"),
    ]

    operations = [
        migrations.CreateModel(
            name="VirtualAccount",
            fields=[
                (
                    "id",
                    models.CharField(
                        db_index=True,
                        default=common.kgs.generate_unique_id,
                        editable=False,
                        max_length=50,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        db_index=True, default=django.utils.timezone.now
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("bank_code", models.CharField(max_length=6)),
                (
                    "bank_name",
                    models.CharField(
                        choices=[
                            ("wema", "wema"),
                            ("kolomoni", "kolomoni"),
                            ("access", "access"),
                        ],
                        db_index=True,
                    ),
                ),
                ("account_number", models.Char<PERSON>ield(db_index=True, max_length=10)),
                ("account_email", models.Char<PERSON><PERSON>(max_length=255)),
                ("account_reference", models.CharField(max_length=60)),
                ("account_name", models.CharField(max_length=255)),
                ("bvn", models.CharField(max_length=11)),
                ("is_static", models.BooleanField(default=True)),
                ("is_for_wallet_funding", models.BooleanField(default=False)),
                (
                    "business",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="virtual_accounts",
                        to="business.business",
                    ),
                ),
            ],
            options={
                "unique_together": {("account_number", "bank_name")},
            },
        ),
    ]
