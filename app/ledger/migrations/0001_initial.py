# Generated by Django 5.1.7 on 2025-05-29 22:17

import common.kgs
import django.db.models.deletion
import django.utils.timezone
import ledger.enums
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("transaction", "0005_airtimevastransaction"),
        ("wallet", "0002_alter_wallet_unique_together"),
    ]

    operations = [
        migrations.CreateModel(
            name="Ledger",
            fields=[
                (
                    "id",
                    models.CharField(
                        db_index=True,
                        default=common.kgs.generate_unique_id,
                        editable=False,
                        max_length=50,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        db_index=True, default=django.utils.timezone.now
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "type",
                    models.CharField(
                        choices=ledger.enums.LedgerTypeEnum.choices,
                        db_index=True,
                        max_length=30,
                    ),
                ),
                ("balance", models.DecimalField(decimal_places=2, max_digits=20)),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="LedgerTransaction",
            fields=[
                (
                    "id",
                    models.CharField(
                        db_index=True,
                        default=common.kgs.generate_unique_id,
                        editable=False,
                        max_length=50,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        db_index=True, default=django.utils.timezone.now
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("amount", models.DecimalField(decimal_places=2, max_digits=20)),
                ("narration", models.TextField()),
                ("old_balance", models.DecimalField(decimal_places=2, max_digits=20)),
                ("new_balance", models.DecimalField(decimal_places=2, max_digits=20)),
                (
                    "ledger",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="ledger_transactions",
                        to="ledger.ledger",
                    ),
                ),
                (
                    "source_transaction",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="ledger_transactions",
                        to="transaction.transaction",
                    ),
                ),
                (
                    "wallet",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="ledger_transactions",
                        to="wallet.wallet",
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
    ]
