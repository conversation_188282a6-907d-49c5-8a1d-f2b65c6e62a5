from common.models import AuditableModel
from django.db import models, transaction
from django.db.models import F
from ledger.enums import LedgerTypeEnum
from transaction.models.base import Transaction


class Ledger(AuditableModel):
    type = models.CharField(
        max_length=30, choices=LedgerTypeEnum.choices(), db_index=True
    )
    balance = models.DecimalField(max_digits=20, decimal_places=2)

    def credit(self, txn: Transaction) -> "LedgerTransaction":
        with transaction.atomic():
            old_balance = self.balance
            self.balance = F("balance") + txn.amount
            self.save()

            # Refresh from DB to get the updated numeric balance
            self.refresh_from_db(fields=["balance"])
            new_balance = self.balance

            return LedgerTransaction.objects.create(
                ledger=self,
                amount=txn.amount,
                narration=txn.narration,
                source_transaction=txn,
                wallet=txn.wallet,
                old_balance=old_balance,
                new_balance=new_balance,
            )

    def debit(self, txn: Transaction) -> "LedgerTransaction":
        with transaction.atomic():
            old_balance = self.balance
            self.balance = F("balance") - txn.amount
            self.save()

            # Refresh to get the evaluated balance
            self.refresh_from_db(fields=["balance"])
            new_balance = self.balance

            return LedgerTransaction.objects.create(
                ledger=self,
                amount=txn.amount,
                narration=txn.narration,
                source_transaction=txn,
                wallet=txn.wallet,
                old_balance=old_balance,
                new_balance=new_balance,
            )

    def __str__(self):
        return f"{self.type} -- {self.balance}"


class LedgerTransaction(AuditableModel):
    ledger = models.ForeignKey(
        Ledger,
        on_delete=models.PROTECT,
        db_index=True,
        related_name="ledger_transactions",
    )
    amount = models.DecimalField(max_digits=20, decimal_places=2)
    narration = models.TextField()
    source_transaction = models.ForeignKey(
        "transaction.Transaction",
        on_delete=models.PROTECT,
        db_index=True,
        related_name="ledger_transactions",
    )
    wallet = models.ForeignKey(
        "wallet.Wallet",
        on_delete=models.PROTECT,
        db_index=True,
        related_name="ledger_transactions",
    )
    old_balance = models.DecimalField(max_digits=20, decimal_places=2)
    new_balance = models.DecimalField(max_digits=20, decimal_places=2)

    def __str__(self):
        return f"{self.source_transaction} -- {self.amount}"
