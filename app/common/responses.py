from dataclasses import dataclass
from typing import Optional

from common.utils import ToDict


@dataclass
class ApiResponse(ToDict):
    def __init__(
        self,
        message: str,
        data: Optional[dict] = None,
        top_level_data: Optional[dict] = None,
        status_code: int = 200,
    ):
        self.message = message
        self.data = data or {}
        self.top_level_data = top_level_data or {}
        self.status_code = status_code

        self.response_block = {
            "success": True,
            "status": "success",
            "message": self.message,
        }

        self._process_data()

    def _process_data(self):
        # Merge top-level data into the response block
        self.response_block.update(self.top_level_data)

        # Always include `data` key if non-empty
        if self.data:
            self.response_block["data"] = self.data

    def get_response(self) -> tuple[int, dict]:
        return self.status_code, self.response_block

    def to_dict(self):
        return self.response_block
