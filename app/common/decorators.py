from functools import wraps

from business.models import Business, OnboardingStage
from rest_framework import status
from rest_framework.response import Response


def merchant_onboarding_required(view_func):
    @wraps(view_func)
    def _wrapped_view(self, request, *args, **kwargs):
        user = request.user
        try:
            business = user.business
        except Business.DoesNotExist:
            return Response(
                {
                    "success": False,
                    "status": "failed",
                    "message": "Authentication Failed.",
                    "data": None,
                },
                status=status.HTTP_403_FORBIDDEN,
            )

        if business.onboarding_stage != OnboardingStage.Completed.value:
            return Response(
                {
                    "success": False,
                    "status": "failed",
                    "message": "Merchant Onboarding Incomplete.",
                    "data": None,
                },
                status=status.HTTP_403_FORBIDDEN,
            )

        request.business = business
        return view_func(self, request, *args, **kwargs)

    return _wrapped_view
