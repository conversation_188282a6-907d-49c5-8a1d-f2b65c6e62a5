# Generated by Django 5.1.7 on 2025-06-02 09:53

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("business", "0005_remove_director_document_alter_director_bvn_and_more"),
        ("transaction", "0006_transaction_charge_transaction_revenue"),
    ]

    operations = [
        migrations.AddField(
            model_name="airtimevastransaction",
            name="business",
            field=models.ForeignKey(
                default="tviyx3a53ij2oq09y014a6i7",
                on_delete=django.db.models.deletion.PROTECT,
                related_name="vas_transactions",
                to="business.business",
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="transaction",
            name="business",
            field=models.ForeignKey(
                default="tviyx3a53ij2oq09y014a6i7",
                on_delete=django.db.models.deletion.PROTECT,
                related_name="transactions",
                to="business.business",
            ),
            preserve_default=False,
        ),
    ]
