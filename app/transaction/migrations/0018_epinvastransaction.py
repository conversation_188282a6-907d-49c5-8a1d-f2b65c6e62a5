# Generated by Django 5.1.7 on 2025-06-09 20:43

import common.kgs
import django.db.models.deletion
import django.utils.timezone
import transaction.enums
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("business", "0006_alter_business_name"),
        ("transaction", "0017_educationvastransaction"),
        ("wallet", "0003_alter_wallet_type"),
    ]

    operations = [
        migrations.CreateModel(
            name="EpinVASTransaction",
            fields=[
                (
                    "id",
                    models.CharField(
                        db_index=True,
                        default=common.kgs.generate_unique_id,
                        editable=False,
                        max_length=50,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        db_index=True, default=django.utils.timezone.now
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "reference",
                    models.CharField(db_index=True, max_length=100, unique=True),
                ),
                (
                    "merchant_reference",
                    models.CharField(db_index=True, max_length=100, unique=True),
                ),
                (
                    "status",
                    models.CharField(
                        choices=transaction.enums.TransactionStatusEnum.choices,
                        db_index=True,
                        max_length=20,
                    ),
                ),
                (
                    "mode",
                    models.CharField(
                        choices=transaction.enums.TransactionModeEnum.choices,
                        db_index=True,
                        max_length=10,
                    ),
                ),
                (
                    "charge",
                    models.DecimalField(decimal_places=2, default=0, max_digits=20),
                ),
                (
                    "net_amount",
                    models.DecimalField(decimal_places=2, default=0, max_digits=20),
                ),
                ("narration", models.TextField()),
                (
                    "network",
                    models.CharField(
                        choices=[
                            ("MTN", "MTN"),
                            ("GLO", "GLO"),
                            ("AIRTEL", "AIRTEL"),
                            ("9MOBILE", "9MOBILE"),
                        ],
                        db_index=True,
                        max_length=20,
                    ),
                ),
                ("quantity", models.IntegerField()),
                (
                    "amount",
                    models.IntegerField(
                        choices=[(100, 100), (200, 200), (500, 500), (1000, 1000)],
                        db_index=True,
                    ),
                ),
                (
                    "business",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="%(class)ss",
                        to="business.business",
                    ),
                ),
                (
                    "wallet",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="%(class)ss",
                        to="wallet.wallet",
                    ),
                ),
            ],
            options={
                "verbose_name": "Epin VAS Transaction",
                "verbose_name_plural": "Epin VAS Transactions",
                "db_table": "epin_vas_transaction",
            },
        ),
    ]
