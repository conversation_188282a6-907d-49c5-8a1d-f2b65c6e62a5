# Generated by Django 5.1.7 on 2025-06-09 08:58

import common.kgs
import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("transaction", "0014_bettingvastransaction_cabletvvastransaction_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="CentralCableTvPlan",
            fields=[
                (
                    "id",
                    models.CharField(
                        db_index=True,
                        default=common.kgs.generate_unique_id,
                        editable=False,
                        max_length=50,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        db_index=True, default=django.utils.timezone.now
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "provider",
                    models.CharField(
                        choices=[
                            ("DSTV", "DSTV"),
                            ("GOTV", "GOTV"),
                            ("STARTIMES", "STARTIMES"),
                        ],
                        db_index=True,
                        max_length=50,
                    ),
                ),
                ("code", models.CharField(db_index=True, max_length=100)),
                ("name", models.CharField(max_length=150)),
                ("price", models.DecimalField(decimal_places=2, max_digits=12)),
                ("duration", models.CharField(max_length=30)),
            ],
            options={
                "ordering": ("provider",),
            },
        ),
    ]
