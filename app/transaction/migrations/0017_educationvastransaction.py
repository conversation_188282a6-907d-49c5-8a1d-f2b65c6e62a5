# Generated by Django 5.1.7 on 2025-06-09 09:52

import common.enums
import common.kgs
import django.db.models.deletion
import django.utils.timezone
import transaction.enums
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("business", "0006_alter_business_name"),
        ("transaction", "0016_alter_centralcabletvplan_options_and_more"),
        ("wallet", "0003_alter_wallet_type"),
    ]

    operations = [
        migrations.CreateModel(
            name="EducationVASTransaction",
            fields=[
                (
                    "id",
                    models.CharField(
                        db_index=True,
                        default=common.kgs.generate_unique_id,
                        editable=False,
                        max_length=50,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        db_index=True, default=django.utils.timezone.now
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "reference",
                    models.CharField(db_index=True, max_length=100, unique=True),
                ),
                (
                    "merchant_reference",
                    models.CharField(db_index=True, max_length=100, unique=True),
                ),
                (
                    "status",
                    models.CharField(
                        choices=transaction.enums.TransactionStatusEnum.choices,
                        db_index=True,
                        max_length=20,
                    ),
                ),
                (
                    "mode",
                    models.CharField(
                        choices=transaction.enums.TransactionModeEnum.choices,
                        db_index=True,
                        max_length=10,
                    ),
                ),
                ("amount", models.DecimalField(decimal_places=2, max_digits=20)),
                (
                    "charge",
                    models.DecimalField(decimal_places=2, default=0, max_digits=20),
                ),
                (
                    "net_amount",
                    models.DecimalField(decimal_places=2, default=0, max_digits=20),
                ),
                ("narration", models.TextField()),
                (
                    "provider",
                    models.CharField(
                        choices=common.enums.EducationProviderEnum.choices,
                        db_index=True,
                        max_length=20,
                    ),
                ),
                (
                    "service_type",
                    models.CharField(
                        choices=common.enums.EducationServiceTypeEnum.choices,
                        db_index=True,
                        max_length=20,
                    ),
                ),
                (
                    "waec_exam_type",
                    models.CharField(
                        blank=True,
                        choices=common.enums.WaecExamTypeEnum.choices,
                        db_index=True,
                        max_length=20,
                        null=True,
                    ),
                ),
                (
                    "jamb_exam_type",
                    models.CharField(
                        blank=True,
                        choices=common.enums.JambExamTypeEnum.choices,
                        db_index=True,
                        max_length=20,
                        null=True,
                    ),
                ),
                ("number_of_pins", models.IntegerField(blank=True, null=True)),
                (
                    "candidate_name",
                    models.CharField(blank=True, max_length=100, null=True),
                ),
                (
                    "candidate_profile_code",
                    models.CharField(blank=True, max_length=100, null=True),
                ),
                (
                    "business",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="%(class)ss",
                        to="business.business",
                    ),
                ),
                (
                    "wallet",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="%(class)ss",
                        to="wallet.wallet",
                    ),
                ),
            ],
            options={
                "verbose_name": "Education VAS Transaction",
                "verbose_name_plural": "Education VAS Transactions",
                "db_table": "education_vas_transaction",
            },
        ),
    ]
