# Generated by Django 5.1.7 on 2025-06-08 23:43

import common.kgs
import django.db.models.deletion
import django.utils.timezone
import transaction.enums
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("business", "0006_alter_business_name"),
        ("transaction", "0012_electricityvastransaction_phone_number"),
        ("wallet", "0003_alter_wallet_type"),
    ]

    operations = [
        migrations.CreateModel(
            name="VirtualAccountVasTransaction",
            fields=[
                (
                    "id",
                    models.CharField(
                        db_index=True,
                        default=common.kgs.generate_unique_id,
                        editable=False,
                        max_length=50,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        db_index=True, default=django.utils.timezone.now
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "reference",
                    models.Char<PERSON>ield(db_index=True, max_length=100, unique=True),
                ),
                (
                    "merchant_reference",
                    models.CharField(db_index=True, max_length=100, unique=True),
                ),
                (
                    "status",
                    models.CharField(
                        choices=transaction.enums.TransactionStatusEnum.choices,
                        db_index=True,
                        max_length=20,
                    ),
                ),
                (
                    "mode",
                    models.CharField(
                        choices=transaction.enums.TransactionModeEnum.choices,
                        db_index=True,
                        max_length=10,
                    ),
                ),
                ("amount", models.DecimalField(decimal_places=2, max_digits=20)),
                (
                    "charge",
                    models.DecimalField(decimal_places=2, default=0, max_digits=20),
                ),
                (
                    "net_amount",
                    models.DecimalField(decimal_places=2, default=0, max_digits=20),
                ),
                ("narration", models.TextField()),
                ("session_id", models.CharField(db_index=True, max_length=40)),
                ("source_account_number", models.CharField(max_length=10)),
                ("source_account_name", models.CharField(max_length=255)),
                ("source_bank_name", models.CharField(max_length=255)),
                ("source_bank_code", models.CharField(max_length=6)),
                ("recipient_account_number", models.CharField(max_length=10)),
                ("recipient_account_name", models.CharField(max_length=255)),
                ("recipient_bank_name", models.CharField(max_length=255)),
                ("recipient_bank_code", models.CharField(max_length=6)),
                ("beneficiary_notified", models.BooleanField(default=False)),
                ("notification_response", models.JSONField(null=True)),
                ("notification_retry_count", models.IntegerField(default=0)),
                (
                    "requery_response_code",
                    models.CharField(
                        help_text="Response for Requery", max_length=5, null=True
                    ),
                ),
                (
                    "requery_response_message",
                    models.CharField(
                        help_text="requery_response_message", max_length=5, null=True
                    ),
                ),
                ("requery_response", models.JSONField(null=True)),
                ("requery_retries_count", models.IntegerField(default=0)),
                (
                    "business",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="%(class)ss",
                        to="business.business",
                    ),
                ),
                (
                    "wallet",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="%(class)ss",
                        to="wallet.wallet",
                    ),
                ),
            ],
            options={
                "ordering": ("-created_at",),
            },
        ),
    ]
