# Generated by Django 5.1.7 on 2025-06-02 12:57

import common.enums
import common.kgs
import django.db.models.deletion
import django.utils.timezone
import transaction.enums
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("business", "0005_remove_director_document_alter_director_bvn_and_more"),
        ("transaction", "0007_airtimevastransaction_business_transaction_business"),
        ("wallet", "0002_alter_wallet_unique_together"),
    ]

    operations = [
        migrations.CreateModel(
            name="DataVASTransaction",
            fields=[
                (
                    "id",
                    models.CharField(
                        db_index=True,
                        default=common.kgs.generate_unique_id,
                        editable=False,
                        max_length=50,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        db_index=True, default=django.utils.timezone.now
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "reference",
                    models.Char<PERSON>ield(db_index=True, max_length=100, unique=True),
                ),
                (
                    "merchant_reference",
                    models.CharField(db_index=True, max_length=100, unique=True),
                ),
                (
                    "status",
                    models.CharField(
                        choices=transaction.enums.TransactionStatusEnum.choices,
                        db_index=True,
                        max_length=20,
                    ),
                ),
                (
                    "mode",
                    models.CharField(
                        choices=transaction.enums.TransactionModeEnum.choices,
                        db_index=True,
                        max_length=10,
                    ),
                ),
                ("amount", models.DecimalField(decimal_places=2, max_digits=20)),
                ("narration", models.TextField()),
                (
                    "network",
                    models.CharField(
                        choices=common.enums.DataNetworkEnum.choices,
                        db_index=True,
                        max_length=20,
                    ),
                ),
                ("phone_number", models.CharField(max_length=20)),
                ("data_code", models.CharField(max_length=50)),
                (
                    "business",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="vas_transactions",
                        to="business.business",
                    ),
                ),
                (
                    "wallet",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="vas_transactions",
                        to="wallet.wallet",
                    ),
                ),
            ],
            options={
                "verbose_name": "Data VAS Transaction",
                "verbose_name_plural": "Data VAS Transactions",
                "db_table": "data_vas_transaction",
            },
        ),
    ]
