# Generated by Django 5.1.7 on 2025-05-29 14:19

import common.enums
import common.kgs
import django.db.models.deletion
import django.utils.timezone
import transaction.enums
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        (
            "transaction",
            "0004_alter_transaction_mode_alter_transaction_status_and_more",
        ),
        ("wallet", "0002_alter_wallet_unique_together"),
    ]

    operations = [
        migrations.CreateModel(
            name="AirtimeVASTransaction",
            fields=[
                (
                    "id",
                    models.CharField(
                        db_index=True,
                        default=common.kgs.generate_unique_id,
                        editable=False,
                        max_length=50,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        db_index=True, default=django.utils.timezone.now
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "reference",
                    models.CharField(db_index=True, max_length=100, unique=True),
                ),
                (
                    "merchant_reference",
                    models.CharField(db_index=True, max_length=100, unique=True),
                ),
                (
                    "status",
                    models.CharField(
                        choices=transaction.enums.TransactionStatusEnum.choices,
                        db_index=True,
                        max_length=20,
                    ),
                ),
                (
                    "mode",
                    models.CharField(
                        choices=transaction.enums.TransactionModeEnum.choices,
                        db_index=True,
                        max_length=10,
                    ),
                ),
                ("amount", models.DecimalField(decimal_places=2, max_digits=20)),
                ("narration", models.TextField()),
                (
                    "network",
                    models.CharField(
                        choices=common.enums.AirtimeNetworkEnum.choices,
                        db_index=True,
                        max_length=20,
                    ),
                ),
                ("phone_number", models.CharField(max_length=20)),
                (
                    "wallet",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="vas_transactions",
                        to="wallet.wallet",
                    ),
                ),
            ],
            options={
                "verbose_name": "Airtime VAS Transaction",
                "verbose_name_plural": "Airtime VAS Transactions",
                "db_table": "airtime_vas_transaction",
            },
        ),
    ]
