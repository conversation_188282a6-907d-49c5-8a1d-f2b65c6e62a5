# Generated by Django 5.1.7 on 2025-05-29 11:13

import transaction.enums
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("transaction", "0003_rename_tx_class_transaction_txn_class"),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name="transaction",
            name="mode",
            field=models.CharField(
                choices=transaction.enums.TransactionModeEnum.choices,
                db_index=True,
                max_length=10,
            ),
        ),
        migrations.AlterField(
            model_name="transaction",
            name="status",
            field=models.CharField(
                choices=transaction.enums.TransactionStatusEnum.choices,
                db_index=True,
                max_length=20,
            ),
        ),
        migrations.AlterField(
            model_name="transaction",
            name="txn_class",
            field=models.CharField(
                choices=transaction.enums.TransactionClassEnum.choices,
                db_index=True,
                max_length=30,
            ),
        ),
    ]
