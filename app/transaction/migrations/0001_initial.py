# Generated by Django 5.1.7 on 2025-05-15 15:20

import common.kgs
import django.db.models.deletion
import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("wallet", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="Transaction",
            fields=[
                (
                    "id",
                    models.CharField(
                        db_index=True,
                        default=common.kgs.generate_unique_id,
                        editable=False,
                        max_length=50,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        db_index=True, default=django.utils.timezone.now
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "reference",
                    models.CharField(db_index=True, max_length=100, unique=True),
                ),
                (
                    "merchant_reference",
                    models.CharField(db_index=True, max_length=100, unique=True),
                ),
                (
                    "status",
                    models.Char<PERSON>ield(
                        choices=[
                            ("PENDING", "PENDING"),
                            ("SUCCESSFUL", "SUCCESSFUL"),
                            ("FAILED", "FAILED"),
                        ],
                        db_index=True,
                        max_length=20,
                    ),
                ),
                (
                    "mode",
                    models.CharField(
                        choices=[("CREDIT", "CREDIT"), ("DEBIT", "DEBIT")],
                        db_index=True,
                        max_length=10,
                    ),
                ),
                (
                    "class_type",
                    models.CharField(
                        choices=[
                            ("INWARD_TRANSFER", "INWARD_TRANSFER"),
                            ("TRANSFER", "TRANSFER"),
                            ("AIRTIME", "AIRTIME"),
                            ("DATA", "DATA"),
                            ("BETTING", "BETTING"),
                            ("ELECTRICITY_PREPAID", "ELECTRICITY_PREPAID"),
                            ("ELECTRICITY_POSTPAID", "ELECTRICITY_POSTPAID"),
                            ("CABLE_TV", "CABLE_TV"),
                            ("SME_DATA", "SME_DATA"),
                            ("KYC", "KYC"),
                            ("EDUCATION", "EDUCATION"),
                            ("EPIN", "EPIN"),
                            ("RECURRING_DEBIT", "RECURRING_DEBIT"),
                        ],
                        db_index=True,
                        max_length=30,
                    ),
                ),
                ("type", models.CharField(db_index=True, max_length=50)),
                ("amount", models.DecimalField(decimal_places=2, max_digits=20)),
                ("old_balance", models.DecimalField(decimal_places=2, max_digits=20)),
                ("new_balance", models.DecimalField(decimal_places=2, max_digits=20)),
                ("narration", models.TextField()),
                (
                    "is_wallet_impacted",
                    models.BooleanField(db_index=True, default=True),
                ),
                (
                    "wallet",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="transactions",
                        to="wallet.wallet",
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
            bases=(models.Model,),
        ),
    ]
