from common.models import AuditableModel
from django.db import models
from transaction.enums import (
    TransactionClassEnum,
    TransactionModeEnum,
    TransactionStatusEnum,
)


class Transaction(AuditableModel):
    wallet = models.ForeignKey(
        "wallet.Wallet",
        on_delete=models.PROTECT,
        related_name="transactions",
        db_index=True,
    )
    business = models.ForeignKey(
        "business.Business",
        on_delete=models.PROTECT,
        related_name="transactions",
        db_index=True,
    )
    reference = models.CharField(max_length=100, unique=True, db_index=True)
    merchant_reference = models.CharField(max_length=100, unique=True, db_index=True)
    status = models.CharField(
        max_length=20, choices=TransactionStatusEnum.choices, db_index=True
    )
    mode = models.CharField(
        max_length=10, choices=TransactionModeEnum.choices, db_index=True
    )
    txn_class = models.Char<PERSON>ield(
        max_length=30, choices=TransactionClassEnum.choices, db_index=True
    )  # Airtime, Data, Betting etc
    type = models.Cha<PERSON><PERSON><PERSON>(max_length=50, db_index=True)  # GOTV, DSTV, MTN etc
    charge = models.DecimalField(max_digits=20, decimal_places=2, default=0)
    revenue = models.Decimal<PERSON>ield(max_digits=20, decimal_places=2, default=0)
    amount = models.DecimalField(max_digits=20, decimal_places=2)
    net_amount = models.DecimalField(max_digits=20, decimal_places=2, default=0)
    old_balance = models.DecimalField(max_digits=20, decimal_places=2)
    new_balance = models.DecimalField(max_digits=20, decimal_places=2)
    narration = models.TextField()
    is_wallet_impacted = models.BooleanField(default=True, db_index=True)

    def __str__(self):
        return f"{self.reference} -- {self.txn_class} -- {self.type} -- {self.status}"


class VASTransaction(AuditableModel):
    wallet = models.ForeignKey(
        "wallet.Wallet",
        on_delete=models.PROTECT,
        related_name="%(class)ss",  # avoids reverse accessor clash in subclasses
        db_index=True,
    )
    business = models.ForeignKey(
        "business.Business",
        on_delete=models.PROTECT,
        related_name="%(class)ss",  # avoids reverse accessor clash in subclasses
        db_index=True,
    )
    reference = models.CharField(max_length=100, unique=True, db_index=True)
    merchant_reference = models.CharField(max_length=100, unique=True, db_index=True)
    status = models.CharField(
        max_length=20, choices=TransactionStatusEnum.choices, db_index=True
    )
    mode = models.CharField(
        max_length=10, choices=TransactionModeEnum.choices, db_index=True
    )
    amount = models.DecimalField(max_digits=20, decimal_places=2)
    charge = models.DecimalField(max_digits=20, decimal_places=2, default=0)
    net_amount = models.DecimalField(max_digits=20, decimal_places=2, default=0)
    narration = models.TextField()

    class Meta:
        abstract = True
