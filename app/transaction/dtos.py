from dataclasses import dataclass
from typing import Optional

from business.models import Business
from transaction.enums import TransactionClassEnum
from wallet.models import Wallet


@dataclass
class CreateBaseTransactionParams:
    wallet: Wallet
    business: Business
    amount: str
    txn_class: TransactionClassEnum
    type: str
    narration: str
    reference: Optional[str] = None  # This represents the merchant's reference
