import random
from datetime import datetime

from core import settings
from ledger.enums import LedgerTypeEnum
from ledger.models import Ledger


def get_transaction_id():
    client_code = settings.EASYPAY_CLIENT_CODE
    timestamp = datetime.now().strftime("%y%m%d%H%M%S")
    rand12 = f"{random.randint(121212, 989898)}{random.randint(121212, 989898)}"
    return f"{client_code}{timestamp}{rand12}"


def get_ledger(ledger_type: LedgerTypeEnum) -> Ledger:
    try:
        ledger = Ledger.objects.get(type=ledger_type)
    except Ledger.DoesNotExist:
        raise Exception("Ledger does not exist")
    return ledger
