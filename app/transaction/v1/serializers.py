import logging

from rest_framework import serializers
from transaction.enums import TransactionClassEnum
from transaction.models import AirtimeVASTransaction, Transaction

logger = logging.getLogger(__name__)


class TransactionSerializer(serializers.ModelSerializer):

    class Meta:
        model = Transaction
        fields = "__all__"


class TransactionOverviewSerializer(serializers.Serializer):
    total_value = serializers.FloatField()
    total_count = serializers.IntegerField()
    pending_count = serializers.IntegerField()
    failed_count = serializers.IntegerField()
    successful_count = serializers.IntegerField()


class AirtimeVASTransactionSerializer(serializers.ModelSerializer):
    class Meta:
        model = AirtimeVASTransaction
        fields = "__all__"


TXN_CLASS_SERIALIZER_MAP = {
    TransactionClassEnum.AIRTIME.value: {
        "model": AirtimeVASTransaction,
        "serializer": AirtimeVASTransactionSerializer,
    },
}


class TransactionPolymorphicSerializer(serializers.ModelSerializer):
    class Meta:
        model = Transaction
        fields = "__all__"

    def to_representation(self, instance):
        txn_class = getattr(instance, "txn_class", None)
        mapping = TXN_CLASS_SERIALIZER_MAP.get(txn_class)

        if mapping:
            model_cls = mapping["model"]
            serializer_cls = mapping["serializer"]

            detailed_instance = self._get_detailed_instance(model_cls, instance)

            if detailed_instance:
                data = serializer_cls(detailed_instance, context=self.context).data
                data["base_txn_id"] = str(instance.id)
                return data

        data = TransactionSerializer(instance, context=self.context).data
        data["base_txn_id"] = str(instance.id)
        return data

    def _get_detailed_instance(self, model_cls, instance):
        lookup_fields = ("id", "reference", "merchant_reference")
        for field in lookup_fields:
            value = getattr(instance, field, None)
            if value:
                try:
                    return model_cls.objects.get(**{field: value})
                except model_cls.DoesNotExist:
                    continue
        return None
