# Generated by Django 5.1.7 on 2025-06-10 22:19

import common.kgs
import django.db.models.deletion
import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("business", "0006_alter_business_name"),
    ]

    operations = [
        migrations.AddField(
            model_name="business",
            name="status",
            field=models.CharField(
                blank=True,
                choices=[
                    ("Inactive", "Inactive"),
                    ("Verified", "Verified"),
                    ("Active", "Active"),
                ],
                default="Inactive",
                max_length=20,
                null=True,
            ),
        ),
        migrations.CreateModel(
            name="APIConfig",
            fields=[
                (
                    "id",
                    models.CharField(
                        db_index=True,
                        default=common.kgs.generate_unique_id,
                        editable=False,
                        max_length=50,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        db_index=True, default=django.utils.timezone.now
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("private_key", models.Char<PERSON>ield(blank=True, null=True)),
                ("public_key", models.<PERSON>r<PERSON><PERSON>(blank=True, db_index=True, null=True)),
                ("webhook_url", models.URLField(blank=True, null=True)),
                ("webhook_signature", models.TextField(blank=True, null=True)),
                ("whitelisted_ips", models.CharField(blank=True, null=True)),
                (
                    "business",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="business_api_keys",
                        to="business.business",
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
    ]
