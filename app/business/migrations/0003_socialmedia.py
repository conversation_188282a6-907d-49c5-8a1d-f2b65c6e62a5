# Generated by Django 5.1.7 on 2025-05-19 14:49

import common.kgs
import django.db.models.deletion
import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("business", "0002_alter_business_options_business_city_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="SocialMedia",
            fields=[
                (
                    "id",
                    models.CharField(
                        db_index=True,
                        default=common.kgs.generate_unique_id,
                        editable=False,
                        max_length=50,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        db_index=True, default=django.utils.timezone.now
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "channel",
                    models.CharField(
                        choices=[
                            ("Facebook", "Facebook"),
                            ("Twitter", "Twitter"),
                            ("Instagram", "Instagram"),
                            ("LinkedIn", "LinkedIn"),
                        ],
                        max_length=50,
                    ),
                ),
                ("url", models.URLField(max_length=255)),
                (
                    "business",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="social_media_channels",
                        to="business.business",
                    ),
                ),
            ],
            options={
                "verbose_name_plural": "Social Media Channels",
                "ordering": ("-created_at",),
                "unique_together": {("business", "channel")},
            },
        ),
    ]
