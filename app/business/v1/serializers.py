import logging

import pyotp
from business.enums import (
    DocumentName,
    DocumentStatus,
    SocialMediaChannel,
)
from business.handlers.api_config_handler import APIConfigHandler
from business.handlers.onboarding_workflow import OnboardingWorkflowHandler
from business.handlers.social_media import SocialMediaHandler
from business.models import (
    APIConfig,
    Business,
    Director,
    Document,
    SettlementDetail,
    SocialMedia,
)
from django.db import transaction
from email_validator import EmailNotValidError, validate_email
from pykolofinance.common.helpers import clean_phone_number
from rest_framework import serializers
from user.enums import Auth2FATypeEnums
from user.models import User

logger = logging.getLogger(__name__)


class BusinessInformationSerializer(serializers.ModelSerializer):
    class Meta:
        model = Business
        fields = (
            "name",
            "email",
            "description",
            "phone",
            "rc_number",
            "street",
            "city",
            "state",
            "office_address",
            "website",
            "postal_code",
            "state",
        )

    def validate(self, attrs):
        data = super().validate(attrs)
        data["phone"] = clean_phone_number(attrs.get("phone").strip())

        if (
            Business.objects.filter(phone=data["phone"])
            .exclude(pk=self.instance.pk if self.instance else None)
            .exists()
        ):
            raise serializers.ValidationError({"phone": "Phone number already exists"})

        if (
            Business.objects.filter(email=data["email"])
            .exclude(pk=self.instance.pk if self.instance else None)
            .exists()
        ):
            raise serializers.ValidationError({"email": "Email already exists"})

        try:
            validated_email = validate_email(data["email"])
            data["email"] = validated_email.normalized
        except EmailNotValidError as e:
            raise serializers.ValidationError({"email": str(e)})

        return data

    def update(self, instance, validated_data):
        instance: Business = instance
        instance.email = validated_data.get("email")
        instance.description = validated_data.get("description")
        instance.phone = validated_data.get("phone")
        instance.rc_number = validated_data.get("rc_number")
        instance.street = validated_data.get("street")
        instance.city = validated_data.get("city")
        instance.state = validated_data.get("state")
        instance.office_address = validated_data.get("office_address")
        instance.postal_code = validated_data.get("postal_code")
        instance.website = validated_data.get("website")
        instance.save()
        OnboardingWorkflowHandler(instance).advance_from_business_information()

        return instance


class UploadDocumentSerializer(serializers.ModelSerializer):
    document_name = serializers.ChoiceField(
        required=True, choices=DocumentName.for_upload_documents_screen()
    )
    document = serializers.FileField(required=True)

    class Meta:
        model = Document
        fields = (
            "document",
            "document_name",
        )

    def create(self, validated_data):
        user: User = self.context["user"]
        business: Business = user.business

        with transaction.atomic():
            Document.objects.update_or_create(
                business=business,
                document_name=validated_data["document_name"],
                defaults={
                    "document": validated_data["document"],
                    "status": DocumentStatus.Pending.value,
                },
            )

            OnboardingWorkflowHandler(business).advance_from_documentation()

        return business


class DocumentMiniSerializer(serializers.ModelSerializer):
    class Meta:
        model = Document
        fields = ("document", "document_name", "rejection_message", "status")


class ListSocialMediaSerializer(serializers.ListSerializer):
    def create(self, validated_data):
        results = []
        for item in validated_data:
            serializer = self.child
            instance = serializer.create(item)
            results.append(instance)
        return results


class SocialMediaSerializer(serializers.ModelSerializer):
    channel = serializers.ChoiceField(choices=SocialMediaChannel.choices())

    class Meta:
        model = SocialMedia
        fields = (
            "id",
            "channel",
            "url",
        )
        read_only_fields = (
            "id",
            "business",
        )

    def create(self, validated_data):
        user: User = self.context["user"]
        business: Business = user.business
        validated_data["business"] = business

        SocialMediaHandler().add(business, validated_data)

        return validated_data


class MultipleSocialMediaSerializer(serializers.Serializer):
    social_medias = SocialMediaSerializer(many=True)

    def validate(self, attrs):
        data = super().validate(attrs)
        if not data["social_medias"]:
            raise serializers.ValidationError(
                "At least one social media item is required."
            )

        return data

    def create(self, validated_data):
        user: User = self.context["user"]
        business: Business = user.business
        data = validated_data["social_medias"]

        for _data in data:
            SocialMediaHandler().add(business, _data)

        return data


class AddDirectorSerializer(serializers.ModelSerializer):
    nin_file = serializers.FileField(required=True)

    class Meta:
        model = Director
        fields = ("name", "email", "phone", "bvn", "nin_file")
        read_only_fields = ("business",)

    def validate(self, attrs):
        data = super().validate(attrs)
        data["phone"] = clean_phone_number(attrs.get("phone").strip())

        user: User = self.context["user"]
        business: Business = user.business

        if Director.objects.filter(phone=data["phone"], business=business).exists():
            raise serializers.ValidationError({"phone": "Phone number already exists"})

        if Director.objects.filter(email=data["email"], business=business).exists():
            raise serializers.ValidationError({"email": "Email already exists"})

        if Director.objects.filter(bvn=data["bvn"], business=business).exists():
            raise serializers.ValidationError({"email": "Bvn already exists"})

        try:
            validated_email = validate_email(data["email"])
            data["email"] = validated_email.normalized
        except EmailNotValidError as e:
            raise serializers.ValidationError({"email": str(e)})

        return data

    def create(self, validated_data):
        user: User = self.context["user"]
        business: Business = user.business
        nin_file = validated_data.pop("nin_file")
        validated_data["business"] = business

        with transaction.atomic():
            document, _ = Document.objects.update_or_create(
                business=business,
                document_name=DocumentName.DirectorNin.value,
                defaults={
                    "document": nin_file,
                    "status": DocumentStatus.Pending.value,
                },
            )
            Director.objects.create(**validated_data)

            OnboardingWorkflowHandler(business).advance_from_directors()

        return business


class DirectorListSerializer(serializers.ModelSerializer):
    """Serializer for listing directors"""

    class Meta:
        model = Director
        fields = (
            "id",
            "name",
            "email",
            "phone",
            "bvn",
        )
        read_only_fields = fields


class SettlementDetailsSerializer(serializers.ModelSerializer):
    class Meta:
        model = SettlementDetail
        fields = (
            "bank_name",
            "bank_code",
            "account_number",
            "account_name",
        )


class AddSettlementDetailsSerializer(serializers.ModelSerializer):
    class Meta:
        model = SettlementDetail
        fields = (
            "bank_code",
            "account_number",
        )

    def validate(self, attrs):

        verify_serializer = VerifyAccountNumberSerializer(data=attrs)
        verify_serializer.is_valid(raise_exception=True)

        verified_data = verify_serializer.validated_data

        attrs["account_name"] = verified_data.get("account_name")
        attrs["bank_name"] = verified_data.get("bank_name")

        return attrs

    def create(self, validated_data):
        user: User = self.context["user"]
        business: Business = user.business

        with transaction.atomic():
            SettlementDetail.objects.update_or_create(
                business=business,
                account_number=validated_data["account_number"],
                bank_code=validated_data["bank_code"],
                account_name=validated_data["account_name"],
                bank_name=validated_data["bank_name"],
            )

            OnboardingWorkflowHandler(business).complete_onboarding()

        return business


class VerifyAccountNumberSerializer(serializers.Serializer):
    bank_code = serializers.CharField()
    account_number = serializers.CharField()

    def validate(self, attrs):
        data = super().validate(attrs)
        account_number = data["account_number"]
        bank_code = data["bank_code"]

        if len(account_number) != 10:
            raise serializers.ValidationError(
                {"account_number": "Invalid account number"}
            )

        if len(bank_code) not in (3, 6):
            raise serializers.ValidationError({"bank_code": "Invalid bank code"})

        # TODO: Talk to vas-gate for account number verification
        data["account_name"] = "Sage Dev Test Account"
        data["bank_name"] = "Sagecloud Test Bank"

        return data

    def create(self, validated_data):
        return validated_data


class GeneratePrivateKeySerializer(serializers.Serializer):
    otp = serializers.CharField(required=True)

    def save(self, **kwargs):
        user = self.context["user"]
        otp = self.validated_data["otp"]

        match user.two_factor_auth_type:
            case Auth2FATypeEnums.TOTP:
                totp = pyotp.TOTP(user.plain_two_factor_auth_secret).now()
                if str(totp) != str(otp):
                    logger.info(f"TOTP {str(totp)} != OTP {str(otp)}")
                    raise serializers.ValidationError(
                        {
                            "otp": "OTP is invalid or expired. Check your Authenticator app"
                        }
                    )
            case _:
                raise serializers.ValidationError(
                    {"user": "Invalid authentication type"}
                )

        business: Business = user.business
        private_key = APIConfigHandler().save_private_key(business)

        return private_key


class APIConfigSettingsSerializer(serializers.ModelSerializer):
    class Meta:
        model = APIConfig
        fields = ("webhook_url", "webhook_signature", "whitelisted_ips")

    def create(self, validated_data):
        business = self.context["business"]
        APIConfig.objects.update_or_create(business=business, defaults=validated_data)

        return validated_data
